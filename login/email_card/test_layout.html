<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>布局测试 - 手动配置参数</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .test-container {
            max-width: 880px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 20px;
            backdrop-filter: blur(20px);
        }
        
        .test-title {
            color: white;
            text-align: center;
            margin-bottom: 20px;
            font-size: 24px;
            font-weight: 600;
        }
        
        .test-info {
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
            margin-bottom: 30px;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .resize-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .resize-btn {
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            color: white;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;
        }
        
        .resize-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">手动配置参数布局测试</h1>
        <div class="test-info">
            点击下方按钮测试不同屏幕尺寸下的响应式布局效果<br>
            已修复的问题：文本溢出、网格布局优化、响应式断点增强
        </div>
        
        <div class="resize-buttons">
            <button class="resize-btn" onclick="resizeWindow(1200, 800)">桌面 (1200px)</button>
            <button class="resize-btn" onclick="resizeWindow(1024, 800)">平板横屏 (1024px)</button>
            <button class="resize-btn" onclick="resizeWindow(768, 800)">平板竖屏 (768px)</button>
            <button class="resize-btn" onclick="resizeWindow(640, 800)">大手机 (640px)</button>
            <button class="resize-btn" onclick="resizeWindow(480, 800)">小手机 (480px)</button>
        </div>
        
        <!-- 手动配置参数测试区域 -->
        <div class="manual-config-section">
            <div class="config-title">
                <i class="fas fa-tools"></i>
                <span>手动配置参数</span>
            </div>

            <!-- SMTP 配置 -->
            <div class="config-group-manual">
                <div class="config-group-title">
                    <i class="fas fa-paper-plane"></i>
                    <span>SMTP 发送服务器配置</span>
                </div>
                <div class="manual-config-grid">
                    <div class="email-input-wrapper manual-input">
                        <div class="input-icon">
                            <i class="fas fa-server"></i>
                        </div>
                        <div class="input-divider"></div>
                        <input type="text" placeholder="smtp.example.com" class="email-input" value="very-long-smtp-server-name.example.com">
                    </div>
                    <div class="email-input-wrapper manual-input">
                        <div class="input-icon">
                            <i class="fas fa-plug"></i>
                        </div>
                        <div class="input-divider"></div>
                        <input type="text" placeholder="465" class="email-input" value="465">
                    </div>
                    <div class="email-input-wrapper manual-input">
                        <div class="input-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="input-divider"></div>
                        <select class="email-input-select">
                            <option value="ssl">SSL/TLS (推荐)</option>
                            <option value="starttls">STARTTLS</option>
                            <option value="none">无加密</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- IMAP 配置 -->
            <div class="config-group-manual">
                <div class="config-group-title">
                    <i class="fas fa-inbox"></i>
                    <span>IMAP 接收服务器配置</span>
                </div>
                <div class="manual-config-grid">
                    <div class="email-input-wrapper manual-input">
                        <div class="input-icon">
                            <i class="fas fa-server"></i>
                        </div>
                        <div class="input-divider"></div>
                        <input type="text" placeholder="imap.example.com" class="email-input" value="very-long-imap-server-name.example.com">
                    </div>
                    <div class="email-input-wrapper manual-input">
                        <div class="input-icon">
                            <i class="fas fa-plug"></i>
                        </div>
                        <div class="input-divider"></div>
                        <input type="text" placeholder="993" class="email-input" value="993">
                    </div>
                    <div class="email-input-wrapper manual-input">
                        <div class="input-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="input-divider"></div>
                        <select class="email-input-select">
                            <option value="ssl">SSL/TLS (推荐)</option>
                            <option value="starttls">STARTTLS</option>
                            <option value="none">无加密</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function resizeWindow(width, height) {
            if (window.resizeTo) {
                window.resizeTo(width, height);
            } else {
                // 如果无法调整窗口大小，至少可以在控制台显示信息
                console.log(`建议将浏览器窗口调整为 ${width}x${height} 像素来测试响应式布局`);
                alert(`请手动将浏览器窗口调整为 ${width}px 宽度来测试响应式布局`);
            }
        }
        
        // 显示当前窗口尺寸
        function showWindowSize() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            console.log(`当前窗口尺寸: ${width}x${height}`);
        }
        
        window.addEventListener('resize', showWindowSize);
        showWindowSize();
    </script>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</body>
</html>
