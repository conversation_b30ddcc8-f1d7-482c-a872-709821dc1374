<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 跨境运营助手</title>
    <!-- 资源预加载优化 -->
    <link rel="preconnect" href="https://cdn.staticfile.net">
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="dns-prefetch" href="https://cdn.staticfile.net">
    <link rel="dns-prefetch" href="https://cdn.jsdelivr.net">
    
    <!-- 核心CSS框架 - 优化加载顺序 -->
    <link rel="stylesheet" href="https://cdn.staticfile.net/font-awesome/6.5.1/css/all.min.css" crossorigin="anonymous" media="print" onload="this.media='all'">
    <noscript><link rel="stylesheet" href="https://cdn.staticfile.net/font-awesome/6.5.1/css/all.min.css" crossorigin="anonymous"></noscript>
    <link rel="stylesheet" href="https://cdn.staticfile.net/tailwindcss/2.2.9/tailwind.min.css" crossorigin="anonymous">
    
    <!-- 项目基础样式 (注意路径调整) -->
    <link rel="stylesheet" href="styles/style.css">
    <link rel="stylesheet" href="styles/style-enhanced.css">
    <link rel="stylesheet" href="styles/performance-enhanced.css">
    
    <!-- 主题系统样式 (注意路径调整) -->
    <link rel="stylesheet" href="themes/theme-switcher.css">
    <link rel="stylesheet" href="themes/alien-effects.css">
    <link rel="stylesheet" href="themes/dark-theme-gradient.css">
    <link rel="stylesheet" href="themes/glassmorphism-unified.css">

    <!-- Remix Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" media="print" onload="this.media='all'">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>📧</text></svg>">
    
    <!-- 自定义补丁样式 -->
    <style>
    /* 兼容不支持 flex-gap 的浏览器 */
    .input-icon + .input-divider {
        margin-left: 12px;
    }
    .input-divider + .login-input-new {
        margin-left: 12px;
    }
    #verification-input-new {
        flex: 1 1 auto !important;
        min-width: 0 !important;
    }
    /* 确保页面在加载时可见 */
    #login-page, #register-page {
        display: block;
    }

    /* 选项卡样式 - 简约设计 */
    .login-tabs {
        display: flex;
        margin-bottom: 24px;
        position: relative;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        padding: 4px;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .login-tabs::before {
        content: '';
        position: absolute;
        top: 4px;
        left: 4px;
        width: calc(50% - 4px);
        height: calc(100% - 8px);
        background: rgba(25, 118, 210, 0.8);
        border-radius: 8px;
        transition: transform 0.3s ease;
        z-index: 1;
        transform: translateX(0);
    }

    .login-tabs[data-active="password"]::before {
        transform: translateX(calc(100% + 4px));
    }

    .tab-item {
        flex: 1;
        text-align: center;
        padding: 12px 16px;
        cursor: pointer;
        color: rgba(255, 255, 255, 0.7);
        font-size: 14px;
        font-weight: 500;
        transition: color 0.2s ease;
        border-radius: 8px;
        position: relative;
        z-index: 2;
        background: transparent;
    }

    .tab-item:hover {
        color: rgba(255, 255, 255, 0.9);
    }

    .tab-item.active {
        color: rgba(255, 255, 255, 1);
        font-weight: 600;
    }



    /* 选项卡响应式优化 */
    @media (max-width: 768px) {
        .login-tabs {
            padding: 3px;
            border-radius: 10px;
        }

        .login-tabs::before {
            top: 3px;
            left: 3px;
            width: calc(50% - 3px);
            height: calc(100% - 6px);
            border-radius: 7px;
        }

        .login-tabs[data-active="password"]::before {
            transform: translateX(calc(100% + 3px));
        }

        .tab-item {
            padding: 10px 12px;
            font-size: 13px;
            border-radius: 7px;
        }
    }

    /* 选项卡内容样式 */
    .tab-content {
        display: none;
        opacity: 0;
        transition: opacity 0.2s ease;
    }

    .tab-content.active {
        display: block;
        opacity: 1;
    }

    /* 统一登录内容区域样式 */
    .unified-login-content {
        padding-top: 12px;
        padding-bottom: 12px;
    }

    .unified-login-content .login-input-wrapper {
        margin-bottom: 20px;
    }

    .unified-login-content .login-input-wrapper:last-of-type {
        margin-bottom: 24px; /* 最后一个输入框与按钮的间距 */
    }

    /* 按钮组样式 */
    .button-group {
        transition: opacity 0.3s ease, transform 0.3s ease;
    }

    .button-group.hidden {
        display: none !important;
    }

    /* 双按钮组特殊样式 */
    .dual-button-group .dual-button-wrapper {
        display: flex;
        gap: 12px;
    }

    .dual-button-group .dual-button-wrapper .login-btn-new {
        flex: 1;
    }

    /* 手机号前缀样式 */
    .phone-prefix {
        color: rgba(255, 255, 255, 0.8);
        font-size: 14px;
        font-weight: 500;
        padding: 0 8px;
        border-right: 1px solid rgba(255, 255, 255, 0.1);
        margin-right: 8px;
    }

    /* 双按钮布局样式 */
    .dual-button-wrapper {
        display: flex;
        gap: 12px;
        margin-top: 20px;
    }

    .dual-button-wrapper .login-btn-new {
        flex: 1;
    }

    .dual-button-wrapper .register-btn {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .dual-button-wrapper .register-btn:hover {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.3);
    }

    /* 忘记密码链接样式 - 文本链接 */
    .forgot-password-link {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: rgba(255, 255, 255, 0.6);
        font-size: 13px;
        font-weight: 400;
        cursor: pointer;
        transition: color 0.3s ease;
        text-decoration: none;
        white-space: nowrap;
        z-index: 3;
    }

    .forgot-password-link:hover {
        color: rgba(255, 255, 255, 0.9);
    }

    /* 密码输入框包含忘记密码链接时的样式调整 */
    .password-wrapper {
        position: relative;
    }

    /* 只有登录页面的密码输入框需要为忘记密码链接留出空间 */
    #password-login-content .password-wrapper {
        padding-right: 120px; /* 为密码切换按钮和忘记密码链接留出空间 */
    }

    #password-login-content .password-wrapper .password-toggle-btn {
        right: 80px; /* 调整密码切换按钮位置 */
    }

    /* 注册页面和重置密码页面的密码输入框，密码切换按钮紧贴右侧 */
    #register-page .password-wrapper .password-toggle-btn,
    #forgot-password-page .password-wrapper .password-toggle-btn {
        right: 12px;
    }

    /* 账号状态提示样式 */
    .username-status {
        margin-top: 8px;
        margin-bottom: 16px;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 13px;
        transition: all 0.3s ease;
    }

    .username-status.available {
        background: rgba(46, 213, 115, 0.1);
        border: 1px solid rgba(46, 213, 115, 0.3);
        color: #2ed573;
    }

    .username-status.unavailable {
        background: rgba(255, 71, 87, 0.1);
        border: 1px solid rgba(255, 71, 87, 0.3);
        color: #ff4757;
    }

    .username-status.checking {
        background: rgba(255, 165, 2, 0.1);
        border: 1px solid rgba(255, 165, 2, 0.3);
        color: #ffa502;
    }

    /* 重置密码步骤样式 */
    .reset-step {
        display: none;
        opacity: 0;
        transform: translateY(10px);
        transition: all 0.3s ease;
    }

    .reset-step.active {
        display: block;
        opacity: 1;
        transform: translateY(0);
    }

    .phone-display {
        background: rgba(25, 118, 210, 0.1);
        border: 1px solid rgba(25, 118, 210, 0.3);
        border-radius: 8px;
        padding: 12px 16px;
        margin-bottom: 20px;
        text-align: center;
    }

    .phone-display p {
        margin: 0;
        color: rgba(255, 255, 255, 0.8);
        font-size: 14px;
    }

    .phone-display span {
        color: #42a5f5;
        font-weight: 600;
    }

    /* 密码强度指示器样式 */
    .password-strength-wrapper {
        margin-top: 8px;
        margin-bottom: 16px;
    }

    .password-strength-bar {
        height: 4px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 2px;
        overflow: hidden;
        margin-bottom: 8px;
    }

    .password-strength-fill {
        height: 100%;
        transition: all 0.3s ease;
        border-radius: 2px;
    }

    .password-strength-fill.weak {
        width: 33%;
        background: #ff4757;
    }

    .password-strength-fill.medium {
        width: 66%;
        background: #ffa502;
    }

    .password-strength-fill.strong {
        width: 100%;
        background: #2ed573;
    }

    .password-strength-text {
        font-size: 12px;
        font-weight: 500;
        transition: color 0.3s ease;
    }

    .password-strength-text.weak {
        color: #ff4757;
    }

    .password-strength-text.medium {
        color: #ffa502;
    }

    .password-strength-text.strong {
        color: #2ed573;
    }

    /* 页面切换动画 */
    #login-page, #register-page, #forgot-password-page {
        transition: opacity 0.3s ease, transform 0.3s ease;
    }

    #login-page.fade-out, #register-page.fade-out, #forgot-password-page.fade-out {
        opacity: 0;
        transform: translateY(-20px);
    }
    </style>
</head>
<body>
    <!-- 登录页面 -->
    <div id="login-page">
        <div class="login-container-new">
            <!-- 背景装饰元素 -->
            <div class="login-background-blur"></div>
            <div class="login-background-shapes">
                <div class="bg-shape bg-shape-1"></div>
                <div class="bg-shape bg-shape-2"></div>
            </div>
            
            <!-- 登录卡片 -->
            <div class="login-card-new">
                <div class="login-header-new">
                    <!-- Logo 区域 -->
                    <div class="login-logo-new">
                        <div class="logo-icon-container">
                            <i class="fas fa-bolt logo-icon-main"></i>
                        </div>
                        <div class="logo-text-container">
                            <span class="logo-text-top">TURING</span>
                            <span class="logo-text-bottom">MARKET</span>
                        </div>
                    </div>
                </div>
                
                <div class="login-content-new">
                    <div class="login-title-section">
                        <h1 class="login-main-title">跨境运营助手</h1>
                        <p class="login-subtitle-new" id="login-subtitle">登录您的账号以继续</p>
                    </div>

                    <!-- 选项卡头部 -->
                    <div class="login-tabs" data-active="sms">
                        <div class="tab-item active" data-tab="sms">短信登录</div>
                        <div class="tab-item" data-tab="password">密码登录</div>
                    </div>

                    <div class="login-form-new">
                        <!-- 统一的登录内容区域 -->
                        <div class="unified-login-content">
                            <!-- 第一个输入框 - 动态切换 -->
                            <div class="login-input-wrapper" id="first-input-wrapper">
                                <div class="input-icon" id="first-input-icon">
                                    <i class="fas fa-mobile-alt" id="first-input-icon-element"></i>
                                </div>
                                <div class="input-divider"></div>
                                <input type="tel" id="first-input" placeholder="请输入手机号" class="login-input-new">
                            </div>

                            <!-- 第二个输入框 - 动态切换 -->
                            <div class="login-input-wrapper" id="second-input-wrapper">
                                <div class="input-icon" id="second-input-icon">
                                    <i class="fas fa-shield-alt" id="second-input-icon-element"></i>
                                </div>
                                <div class="input-divider"></div>
                                <input type="text" id="second-input" placeholder="请输入验证码" class="login-input-new">
                                <!-- 发送验证码按钮 - 仅在短信登录时显示 -->
                                <button class="send-code-btn-new" id="send-code-btn">发送验证码</button>
                                <!-- 密码切换按钮 - 仅在密码登录时显示 -->
                                <button class="password-toggle-btn" id="password-toggle-btn" type="button" style="display: none;">
                                    <i class="ri-eye-off-line"></i>
                                </button>
                                <!-- 忘记密码链接 - 仅在密码登录时显示 -->
                                <a href="#" class="forgot-password-link" id="forgot-password-link" style="display: none;">忘记密码？</a>
                            </div>

                            <!-- 动态按钮组 -->
                            <!-- 单按钮组 - 短信登录时显示 -->
                            <div class="button-group single-button-group" id="single-button-group">
                                <button class="login-btn-new" id="sms-login-btn">
                                    登录/注册
                                </button>
                            </div>

                            <!-- 双按钮组 - 密码登录时显示 -->
                            <div class="button-group dual-button-group" id="dual-button-group" style="display: none;">
                                <div class="dual-button-wrapper">
                                    <button class="login-btn-new register-btn" id="password-register-btn">
                                        注册
                                    </button>
                                    <button class="login-btn-new" id="password-login-btn">
                                        登录
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Google 登录按钮 -->
                        <button class="google-login-btn-new" id="google-login-btn-new">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" class="google-icon">
                                <path fill="#FFC107" d="M43.6 20.2H24v7.5h11.3c-1.1 5.4-5.7 8.2-11.3 8.2-6.6 0-12-5.4-12-12s5.4-12 12-12c2.9 0 5.5 1 7.5 2.7l5.6-5.6C33.2 5.9 28.9 4 24 4 12.9 4 4 12.9 4 24s8.9 20 20 20c11 0 19.9-8 19.9-20 0-1.3-.2-2.7-.3-4z"/>
                                <path fill="#FF3D00" d="M6.3 14.7l6.5 4.8c1.4-4.2 5.3-7.2 9.8-7.2 2.9 0 5.5 1 7.5 2.7l5.6-5.6C31.8 5.9 28.2 4 24 4 16.8 4 10.9 8.7 6.3 14.7z"/>
                                <path fill="#4CAF50" d="M24 44c4.1 0 7.9-1.4 10.8-3.7l-5.6-4.3c-1.6 1.1-3.6 1.8-5.8 1.8-4.5 0-8.5-2.5-10.5-6.2l-6.5 5c4.5 8.9 13.7 14.4 24 14.4z"/>
                                <path fill="#1976D2" d="M43.6 20.2H24v7.5h11.3c-.5 2.6-1.9 4.9-3.9 6.4l5.6 4.3c3.3-3.1 5.4-7.6 5.4-12.7 0-1.3-.2-2.7-.3-4z"/>
                            </svg>
                            使用 Google 账号
                        </button>



                    </div>
                    
                    <!-- 消息提示容器 -->
                    <div id="login-message-container"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 注册页面 -->
    <div id="register-page" style="display: none;">
        <div class="login-container-new">
            <!-- 背景装饰元素 -->
            <div class="login-background-blur"></div>
            <div class="login-background-shapes">
                <div class="bg-shape bg-shape-1"></div>
                <div class="bg-shape bg-shape-2"></div>
            </div>

            <!-- 注册卡片 -->
            <div class="login-card-new">
                <div class="login-header-new">
                    <!-- Logo 区域 -->
                    <div class="login-logo-new">
                        <div class="logo-icon-container">
                            <i class="fas fa-bolt logo-icon-main"></i>
                        </div>
                        <div class="logo-text-container">
                            <span class="logo-text-top">TURING</span>
                            <span class="logo-text-bottom">MARKET</span>
                        </div>
                    </div>
                </div>

                <div class="login-content-new">
                    <div class="login-title-section">
                        <h1 class="login-main-title">创建新账号</h1>
                        <p class="login-subtitle-new">注册您的账号以开始使用</p>
                    </div>

                    <div class="login-form-new">
                        <!-- 手机号输入 -->
                        <div class="login-input-wrapper">
                            <div class="input-icon">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <div class="input-divider"></div>
                            <input type="tel" id="register-phone-input" placeholder="请输入手机号" class="login-input-new">
                        </div>

                        <!-- 验证码输入 -->
                        <div class="login-input-wrapper verification-wrapper">
                            <div class="input-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="input-divider"></div>
                            <input type="text" id="register-verification-input" placeholder="请输入验证码" class="login-input-new verification-input" maxlength="6">
                            <button class="send-code-btn-new" id="register-send-code-btn">发送验证码</button>
                        </div>

                        <!-- 账号设置输入 -->
                        <div class="login-input-wrapper">
                            <div class="input-icon">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="input-divider"></div>
                            <input type="text" id="register-username-input" placeholder="请设置账号（3-20位字符）" class="login-input-new">
                        </div>

                        <!-- 账号可用性提示 -->
                        <div class="username-status" id="username-status" style="display: none;">
                            <div class="status-message" id="username-status-message"></div>
                        </div>

                        <!-- 密码输入 -->
                        <div class="login-input-wrapper password-wrapper">
                            <div class="input-icon">
                                <i class="fas fa-lock"></i>
                            </div>
                            <div class="input-divider"></div>
                            <input type="password" id="register-password-input" placeholder="请设置密码" class="login-input-new password-input">
                            <button class="password-toggle-btn" id="register-password-toggle-btn" type="button">
                                <i class="ri-eye-off-line"></i>
                            </button>
                        </div>

                        <!-- 密码强度指示器 -->
                        <div class="password-strength-wrapper" id="register-password-strength-wrapper">
                            <div class="password-strength-bar">
                                <div class="password-strength-fill" id="register-password-strength-fill"></div>
                            </div>
                            <div class="password-strength-text" id="register-password-strength-text">密码强度：弱</div>
                        </div>

                        <!-- 确认密码输入 -->
                        <div class="login-input-wrapper password-wrapper">
                            <div class="input-icon">
                                <i class="fas fa-lock"></i>
                            </div>
                            <div class="input-divider"></div>
                            <input type="password" id="register-confirm-password-input" placeholder="请确认密码" class="login-input-new password-input">
                            <button class="password-toggle-btn" id="register-confirm-password-toggle-btn" type="button">
                                <i class="ri-eye-off-line"></i>
                            </button>
                        </div>

                        <!-- 注册按钮 -->
                        <button class="login-btn-new" id="register-btn">
                            注册
                        </button>

                        <!-- 返回登录 -->
                        <div class="register-link-wrapper">
                            <p class="register-text">已有账号？ <a href="#" class="register-link" id="back-to-login-link">立即登录</a></p>
                        </div>

                    </div>
                    
                    <!-- 消息提示容器 -->
                    <div id="register-message-container"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 忘记密码页面 -->
    <div id="forgot-password-page" style="display: none;">
        <div class="login-container-new">
            <!-- 背景装饰元素 -->
            <div class="login-background-blur"></div>
            <div class="login-background-shapes">
                <div class="bg-shape bg-shape-1"></div>
                <div class="bg-shape bg-shape-2"></div>
            </div>

            <!-- 忘记密码卡片 -->
            <div class="login-card-new">
                <div class="login-header-new">
                    <!-- Logo 区域 -->
                    <div class="login-logo-new">
                        <div class="logo-icon-container">
                            <i class="fas fa-bolt logo-icon-main"></i>
                        </div>
                        <div class="logo-text-container">
                            <span class="logo-text-top">TURING</span>
                            <span class="logo-text-bottom">MARKET</span>
                        </div>
                    </div>
                </div>

                <div class="login-content-new">
                    <div class="login-title-section">
                        <h1 class="login-main-title">重置密码</h1>
                        <p class="login-subtitle-new">输入账号信息重置您的登录密码</p>
                    </div>

                    <div class="login-form-new">
                        <!-- 账号输入 -->
                        <div class="login-input-wrapper">
                            <div class="input-icon">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="input-divider"></div>
                            <input type="text" id="reset-account-input" placeholder="请输入您的账号" class="login-input-new">
                        </div>

                        <!-- 验证码输入 -->
                        <div class="login-input-wrapper verification-wrapper">
                            <div class="input-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="input-divider"></div>
                            <input type="text" id="reset-verification-input" placeholder="请输入验证码" class="login-input-new verification-input" maxlength="6">
                            <button class="send-code-btn-new" id="reset-send-code-btn">发送验证码</button>
                        </div>

                        <!-- 新密码输入 -->
                        <div class="login-input-wrapper password-wrapper">
                            <div class="input-icon">
                                <i class="fas fa-lock"></i>
                            </div>
                            <div class="input-divider"></div>
                            <input type="password" id="reset-password-input" placeholder="请设置新密码" class="login-input-new password-input">
                            <button class="password-toggle-btn" id="reset-password-toggle-btn" type="button">
                                <i class="ri-eye-off-line"></i>
                            </button>
                        </div>

                        <!-- 密码强度指示器 -->
                        <div class="password-strength-wrapper" id="reset-password-strength-wrapper">
                            <div class="password-strength-bar">
                                <div class="password-strength-fill" id="reset-password-strength-fill"></div>
                            </div>
                            <div class="password-strength-text" id="reset-password-strength-text">密码强度：弱</div>
                        </div>

                        <!-- 确认密码输入 -->
                        <div class="login-input-wrapper password-wrapper">
                            <div class="input-icon">
                                <i class="fas fa-lock"></i>
                            </div>
                            <div class="input-divider"></div>
                            <input type="password" id="reset-confirm-password-input" placeholder="请确认新密码" class="login-input-new password-input">
                            <button class="password-toggle-btn" id="reset-confirm-password-toggle-btn" type="button">
                                <i class="ri-eye-off-line"></i>
                            </button>
                        </div>

                        <!-- 重置密码按钮 -->
                        <button class="login-btn-new" id="reset-password-btn">
                            重置密码
                        </button>

                        <!-- 返回登录 -->
                        <div class="register-link-wrapper">
                            <p class="register-text">想起密码了？ <a href="#" class="register-link" id="back-to-login-from-reset">返回登录</a></p>
                        </div>

                    </div>
                    
                    <!-- 消息提示容器 -->
                    <div id="reset-message-container"></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 依赖的 JS 文件 (注意路径调整) -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="../themes/theme-config.js"></script>
    <script src="../themes/theme-manager.js"></script>
    <script src="script.js"></script>
    <script>
        // 页面加载完成后，显式地初始化登录页面逻辑
        document.addEventListener('DOMContentLoaded', () => {
            if (typeof setupLoginPage === 'function') {
                setupLoginPage();
            } else {
                console.error('Login script not loaded or setupLoginPage function is missing.');
            }
        });
    </script>
</body>
</html> 
